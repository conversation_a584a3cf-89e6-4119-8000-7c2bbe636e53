<!DOCTYPE html>
<!--
    吃豆人游戏 - HTML5 Canvas实现
    这是一个经典的吃豆人游戏，使用HTML5 Canvas和JavaScript开发
    游戏包含多个关卡，智能AI幽灵，以及完整的游戏逻辑
-->
<html lang="zh-CN">
	<head>
		<!-- 设置文档字符编码为UTF-8，确保中文字符正确显示 -->
		<meta charset="utf8">

		<!-- 页面标题，显示在浏览器标签页上 -->
		<title>吃豆人 ・ Pac-Man</title>

		<!-- SEO优化：页面描述，用于搜索引擎索引和社交媒体分享 -->
		<meta name="description" content="吃豆人（Pac-Man）是上世纪80年代一款经典街机游戏，游戏的主角小精灵的形象甚至被作为一种大众文化符号。本游戏使用HTML5完美移植该游戏，设置了多个关卡并改良了幽灵的寻路算法，可作为Canvas游戏学习的案例演示。">

		<!-- SEO优化：关键词，帮助搜索引擎理解页面内容 -->
		<meta name="keywords" content="吃豆人,FC吃豆子,吃豆游戏,Pac-Man,HTML5游戏,Javascript游戏引擎">

		<!-- 设置网站图标 -->
		<link rel="shortcut icon" href="favicon.png">

		<!-- 引入主要的CSS样式文件 -->
		<link rel="stylesheet" href="./static/style/index.css">

		<!--
		防盗链保护代码（已注释）
		用于防止其他网站直接使用本游戏资源
		通过检测域名和iframe嵌套来实现保护
		-->
		<!-- <script type="text/javascript">
			// 自动跳转到指定页面，防止别人盗取链接或直接部署道其他网站的保护措施
            if(location.protocol!='file:'){
                setTimeout(function(){
                    if(!location.hostname.includes('passer-by.com')){
                        location.href = 'http://passer-by.com/';
                    }else if( window.top != window.self ) {
                        window.top.location = self.location.href;
                    }
                },parseInt(3000+15000*Math.random()));
            }
		</script> -->
	</head>
	<body>
		<!-- 主容器：包含整个游戏界面 -->
		<div class="wrapper">
			<!-- 游戏面板：包含游戏画布和相关信息 -->
			<div class="mod-panel">
				<!-- 头部：游戏标题 -->
				<div class="hd">
					<h1>吃豆人 ・ Pac-Man</h1>
				</div>

				<!-- 主体：游戏画布区域 -->
				<div class="bd">
					<!--
					游戏画布：所有游戏图形都在这里渲染
					width="960" height="640" 设置画布尺寸
					如果浏览器不支持Canvas，会显示"不支持画布"文字
					-->
					<canvas id="canvas" width="960" height="640">不支持画布</canvas>
				</div>

				<!-- 底部：操作说明和游戏介绍 -->
				<div class="ft">
					<!-- 操作提示信息 -->
					<div class="info">
						<p>按 [空格键] 暂停或继续</p>
						<p>Press [space] to pause or continue</p>
					</div>

					<!-- 游戏介绍文字 -->
					<div class="intro">
						<p>这款吃豆人游戏的开发是我在学习和探索HTML5游戏的一次尝试，也是对这款儿时经典街机游戏的致敬。游戏大致还原了我印象中Pac-Man的样子，在移植关卡和玩法规则的同时，在游戏中加入了游戏角色动画管理和幽灵的智能寻址算法，实现了幽灵对玩家的围堵。在你玩的过程中，你会发现这些精灵似乎很有想法，它们知道如何彼此协作对你穷追不舍。</p>
						<p>如果你对此感兴趣，可以在Github上关注此项目。我希望能通过游戏和代码与你分享一些我对游戏开发的感悟。</p>
					</div>
				</div>
			</div>

			<!-- 按钮区域：GitHub相关按钮和返回首页链接 -->
			<div class="mod-button">
				<!-- GitHub关注按钮 -->
				<a class="github-button" href="https://github.com/mumuy" data-color-scheme="no-preference: light; light: light; dark: dark;" data-size="large" aria-label="Follow @mumuy on GitHub">Follow @mumuy</a>

				<!-- GitHub Fork按钮 -->
				<a class="github-button" href="https://github.com/mumuy/pacman/fork" data-color-scheme="no-preference: light; light: light; dark: dark;" data-icon="octicon-repo-forked" data-size="large" data-show-count="true" aria-label="Fork mumuy/pacman on GitHub">Fork</a>

				<!-- GitHub Star按钮 -->
				<a class="github-button" href="https://github.com/mumuy/pacman" data-color-scheme="no-preference: light; light: light; dark: dark;" data-icon="octicon-star" data-size="large" data-show-count="true" aria-label="Star mumuy/pacman on GitHub">Star</a>

				<!-- 返回首页按钮 -->
				<a class="btn" href="/">返回首页</a>
			</div>
		</div>

		<!-- JavaScript文件引入 -->
		<!-- 游戏引擎核心文件：包含游戏框架、对象管理、动画系统等 -->
		<script src="./static/script/game.js"></script>

		<!-- 游戏逻辑文件：包含关卡数据、角色行为、游戏规则等 -->
		<script src="./static/script/index.js"></script>

		<!--
		学习版本：移除外部依赖，专注核心游戏逻辑
		原版包含的外部脚本：
		- GitHub按钮脚本（用于显示Star/Fork按钮）
		- 统计脚本（用于访问量统计）
		- 项目管理脚本（作者个人网站功能）

		学习时可以暂时移除这些，专注于游戏本身
		-->
		<!-- <script async defer src="https://buttons.github.io/buttons.js"></script> -->
		<!-- <script type="text/javascript" src="https://passer-by.com/public/script/projects.js"></script> -->
		<!-- <script type="text/javascript" src="https://passer-by.com/public/script/stat.js"></script> -->
	</body>
</html>
