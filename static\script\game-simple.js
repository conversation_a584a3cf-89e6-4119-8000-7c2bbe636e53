/**
 * 简化版游戏引擎 - 学习用
 *
 * 这是一个简化版本，帮助您理解游戏引擎的核心概念
 * 建议按照以下顺序学习和实现：
 * 1. 基础Canvas操作 - 学习如何在画布上绘制图形
 * 2. 游戏对象管理 - 理解如何创建和管理游戏中的各种对象
 * 3. 动画循环 - 掌握游戏动画的实现原理
 * 4. 事件处理 - 学习如何响应用户输入
 * 5. 地图系统 - 实现游戏世界的构建
 */

'use strict'; // 启用严格模式，避免一些JavaScript的陷阱

/* ========================================
   游戏引擎主类 (SimpleGame)
   ======================================== */

/**
 * 简化版游戏引擎构造函数
 *
 * 这个类是整个游戏的核心，负责：
 * - 管理Canvas画布
 * - 控制游戏循环
 * - 管理所有游戏对象
 * - 处理渲染和更新
 *
 * @param {string} canvasId - HTML中Canvas元素的ID
 */
function SimpleGame(canvasId) {
    /* ---- Canvas相关属性 ---- */

    // 获取HTML中的Canvas元素
    // Canvas是HTML5提供的画布元素，用于绘制2D图形
    this.canvas = document.getElementById(canvasId);

    // 获取2D渲染上下文，这是实际进行绘制操作的对象
    // 所有的绘制命令（如drawRect, drawCircle等）都通过ctx执行
    this.ctx = this.canvas.getContext('2d');

    /* ---- 游戏状态管理 ---- */

    // 游戏运行状态标志
    // true = 游戏正在运行，false = 游戏已停止
    this.isRunning = false;

    // 动画帧ID，用于控制和取消动画循环
    // requestAnimationFrame返回的ID，可以用cancelAnimationFrame取消
    this.animationId = null;

    /* ---- 游戏对象管理 ---- */

    // 游戏对象数组，存储所有需要更新和绘制的对象
    // 例如：玩家角色、敌人、道具、UI元素等
    this.objects = [];

    /* ---- Canvas尺寸信息 ---- */

    // 保存Canvas的宽度和高度，方便后续使用
    // 这些值在边界检测、对象定位等场景中经常用到
    this.width = this.canvas.width;   // 画布宽度（像素）
    this.height = this.canvas.height; // 画布高度（像素）
}

/* ========================================
   游戏对象基类 (GameObject)
   ======================================== */

/**
 * 游戏对象基类构造函数
 *
 * 这是所有游戏对象的基础类，定义了游戏对象的通用属性和方法
 * 所有的游戏角色、道具、UI元素都应该继承自这个类
 *
 * 设计模式：这里使用了面向对象编程中的"继承"概念
 * - GameObject是父类（基类）
 * - 具体的游戏对象（如Pacman、Ghost）是子类
 *
 * @param {number} x - 对象的X坐标（像素位置）
 * @param {number} y - 对象的Y坐标（像素位置）
 * @param {number} width - 对象的宽度（像素）
 * @param {number} height - 对象的高度（像素）
 */
function GameObject(x, y, width, height) {
    /* ---- 位置和尺寸属性 ---- */

    // 对象在Canvas中的X坐标（水平位置）
    // 使用 || 运算符提供默认值：如果x为undefined/null/0，则使用0
    this.x = x || 0;

    // 对象在Canvas中的Y坐标（垂直位置）
    // Canvas坐标系：左上角为(0,0)，X轴向右，Y轴向下
    this.y = y || 0;

    // 对象的宽度（像素）
    // 用于碰撞检测、绘制范围计算等
    this.width = width || 20;

    // 对象的高度（像素）
    // 与width一起定义了对象的矩形边界
    this.height = height || 20;

    /* ---- 视觉属性 ---- */

    // 对象的颜色（CSS颜色格式）
    // 可以是十六进制（#FF0000）、RGB（rgb(255,0,0)）、颜色名（red）等
    this.color = '#FF0000'; // 默认红色

    // 对象是否可见
    // false时对象不会被绘制，但仍会执行update逻辑
    this.visible = true;
}

/* ---- GameObject的原型方法 ---- */

/**
 * 游戏对象绘制方法
 *
 * 这个方法负责将对象绘制到Canvas上
 * 每一帧都会被调用，实现对象的视觉呈现
 *
 * 绘制流程：
 * 1. 检查对象是否可见
 * 2. 设置绘制颜色
 * 3. 绘制矩形
 *
 * @param {CanvasRenderingContext2D} ctx - Canvas 2D渲染上下文
 */
GameObject.prototype.draw = function(ctx) {
    // 如果对象不可见，直接返回，不进行绘制
    // 这是一种性能优化，避免绘制不必要的对象
    if (!this.visible) return;

    // 设置填充颜色
    // fillStyle属性决定了后续填充操作使用的颜色
    ctx.fillStyle = this.color;

    // 绘制填充矩形
    // fillRect(x, y, width, height) - 绘制一个填充的矩形
    // 参数：起始X坐标，起始Y坐标，宽度，高度
    ctx.fillRect(this.x, this.y, this.width, this.height);
};

/**
 * 游戏对象更新方法
 *
 * 这个方法在每一帧被调用，用于更新对象的状态
 * 包括位置移动、动画状态变化、逻辑判断等
 *
 * 基类中的update方法是空的，需要子类重写来实现具体逻辑
 * 这是面向对象编程中的"多态"概念：
 * - 不同的子类可以有不同的update实现
 * - 但都遵循相同的接口规范
 */
GameObject.prototype.update = function() {
    // 基类的update方法为空
    // 子类应该重写（override）这个方法来实现具体的更新逻辑
    // 例如：移动、动画、状态检查、碰撞检测等
};

/* ========================================
   SimpleGame的原型方法
   ======================================== */

/**
 * 添加游戏对象到游戏中
 *
 * 将一个游戏对象加入到游戏的管理列表中
 * 加入后的对象会在每一帧被自动更新和绘制
 *
 * @param {GameObject} obj - 要添加的游戏对象（必须继承自GameObject）
 */
SimpleGame.prototype.addObject = function(obj) {
    // 将对象添加到objects数组的末尾
    // push()方法会返回新数组的长度，但这里我们不需要使用返回值
    this.objects.push(obj);
};

/**
 * 从游戏中移除指定的游戏对象
 *
 * 将对象从管理列表中移除，移除后的对象不再被更新和绘制
 * 这在对象被销毁、游戏结束、切换场景时很有用
 *
 * @param {GameObject} obj - 要移除的游戏对象
 */
SimpleGame.prototype.removeObject = function(obj) {
    // 查找对象在数组中的索引位置
    // indexOf()返回第一个匹配元素的索引，如果没找到返回-1
    const index = this.objects.indexOf(obj);

    // 如果找到了对象（索引大于-1）
    if (index > -1) {
        // 使用splice()方法移除数组中的元素
        // splice(start, deleteCount) - 从start位置开始，删除deleteCount个元素
        this.objects.splice(index, 1);
    }
};

/**
 * 游戏主循环 - 游戏引擎的核心
 *
 * 这是整个游戏最重要的方法，负责：
 * 1. 清除上一帧的画面
 * 2. 更新所有游戏对象的状态
 * 3. 绘制所有游戏对象
 * 4. 请求下一帧动画
 *
 * 游戏循环的概念：
 * - 游戏是由连续的"帧"组成的，就像电影一样
 * - 每一帧都要重新绘制整个画面
 * - 通过快速切换帧（通常60fps），产生动画效果
 *
 * requestAnimationFrame的优势：
 * - 与浏览器的刷新率同步（通常60fps）
 * - 当页面不可见时自动暂停，节省性能
 * - 比setTimeout更精确和流畅
 */
SimpleGame.prototype.gameLoop = function() {
    // 检查游戏是否正在运行
    // 如果游戏已停止，直接返回，不继续执行循环
    if (!this.isRunning) return;

    /* ---- 第1步：清除画布 ---- */

    // 清除整个画布区域
    // clearRect(x, y, width, height) - 清除指定矩形区域
    // 这里清除整个画布：从(0,0)开始，宽度和高度为画布的完整尺寸
    this.ctx.clearRect(0, 0, this.width, this.height);

    // 设置背景色为黑色（经典游戏风格）
    this.ctx.fillStyle = '#000000';

    // 绘制黑色背景
    // 虽然clearRect已经清除了画布，但绘制背景确保了一致的视觉效果
    this.ctx.fillRect(0, 0, this.width, this.height);

    /* ---- 第2步：更新所有游戏对象 ---- */

    // 遍历所有游戏对象，调用它们的update方法
    // 这里使用传统的for循环而不是forEach，因为：
    // 1. 性能更好（避免函数调用开销）
    // 2. 在游戏循环中每一帧都执行，性能很重要
    for (let i = 0; i < this.objects.length; i++) {
        this.objects[i].update();
    }

    /* ---- 第3步：绘制所有游戏对象 ---- */

    // 遍历所有游戏对象，调用它们的draw方法
    // 注意：绘制顺序很重要！
    // - 先绘制的对象会被后绘制的对象覆盖
    // - 这就是"图层"的概念：背景 -> 游戏对象 -> UI
    for (let i = 0; i < this.objects.length; i++) {
        this.objects[i].draw(this.ctx);
    }

    /* ---- 第4步：请求下一帧 ---- */

    // 使用箭头函数保持this的正确指向
    // requestAnimationFrame会在下一次重绘之前调用指定的函数
    // 这样就形成了一个循环：gameLoop -> requestAnimationFrame -> gameLoop -> ...
    this.animationId = requestAnimationFrame(() => this.gameLoop());
};

/**
 * 启动游戏
 *
 * 开始游戏循环，让游戏进入运行状态
 * 调用此方法后，游戏会开始自动更新和绘制所有对象
 */
SimpleGame.prototype.start = function() {
    // 设置游戏运行状态为true
    this.isRunning = true;

    // 启动游戏主循环
    // 第一次调用gameLoop，之后gameLoop会通过requestAnimationFrame自我调用
    this.gameLoop();
};

/**
 * 停止游戏
 *
 * 停止游戏循环，让游戏进入暂停状态
 * 游戏对象会保持当前状态，但不再更新和绘制
 */
SimpleGame.prototype.stop = function() {
    // 设置游戏运行状态为false
    // 这会让gameLoop在下一次执行时直接返回
    this.isRunning = false;

    // 如果有正在进行的动画帧请求，取消它
    if (this.animationId) {
        // cancelAnimationFrame取消之前requestAnimationFrame的请求
        // 这确保了游戏循环完全停止，不会有遗留的动画帧
        cancelAnimationFrame(this.animationId);
    }
};

/* ========================================
   示例游戏对象：MovingBox（移动方块）
   ======================================== */

/**
 * 移动方块类 - 用于测试和学习
 *
 * 这是一个继承自GameObject的示例类，展示了：
 * 1. 如何继承GameObject
 * 2. 如何重写update方法实现自定义行为
 * 3. 如何实现简单的物理运动和边界检测
 *
 * 这个方块会在画布中弹跳移动，碰到边界时反弹
 *
 * @param {number} x - 初始X坐标
 * @param {number} y - 初始Y坐标
 */
function MovingBox(x, y) {
    // 调用父类构造函数
    // GameObject.call(this, ...) 相当于 super(...) 在ES6类中
    // 参数：x坐标, y坐标, 宽度30像素, 高度30像素
    GameObject.call(this, x, y, 30, 30);

    /* ---- 移动方块的特有属性 ---- */

    // 设置颜色为吃豆人的经典黄色
    this.color = '#FFE600';

    // X轴移动速度（像素/帧）
    // 正值向右移动，负值向左移动
    this.speedX = 2;

    // Y轴移动速度（像素/帧）
    // 正值向下移动，负值向上移动
    this.speedY = 1;
}

/* ---- 设置继承关系 ---- */

// 创建原型链继承
// Object.create(GameObject.prototype) 创建一个新对象，其原型指向GameObject.prototype
// 这样MovingBox就能访问GameObject的所有方法
MovingBox.prototype = Object.create(GameObject.prototype);

// 修正构造函数引用
// 因为上面的操作会覆盖constructor属性，所以需要手动设置回来
MovingBox.prototype.constructor = MovingBox;

/* ---- 重写父类方法 ---- */

/**
 * 重写update方法 - 实现移动和边界检测
 *
 * 这个方法每帧被调用，实现以下功能：
 * 1. 根据速度更新位置
 * 2. 检测是否碰到画布边界
 * 3. 碰到边界时反转速度方向（实现反弹效果）
 */
MovingBox.prototype.update = function() {
    /* ---- 位置更新 ---- */

    // 根据速度更新X坐标
    // 每帧移动speedX个像素
    this.x += this.speedX;

    // 根据速度更新Y坐标
    // 每帧移动speedY个像素
    this.y += this.speedY;

    /* ---- 边界检测和反弹 ---- */

    // 检测左右边界
    // 左边界：x <= 0（碰到左边）
    // 右边界：x >= 960 - width（碰到右边，需要考虑对象宽度）
    if (this.x <= 0 || this.x >= 960 - this.width) {
        // 反转X轴速度，实现水平反弹
        this.speedX = -this.speedX;
    }

    // 检测上下边界
    // 上边界：y <= 0（碰到顶部）
    // 下边界：y >= 640 - height（碰到底部，需要考虑对象高度）
    if (this.y <= 0 || this.y >= 640 - this.height) {
        // 反转Y轴速度，实现垂直反弹
        this.speedY = -this.speedY;
    }
};

/* ========================================
   使用示例和学习指南
   ======================================== */

/**
 * 基础使用示例：
 *
 * 以下代码展示了如何使用这个简化版游戏引擎：
 *
 * // 第1步：创建游戏引擎实例
 * // 参数是HTML中Canvas元素的ID
 * const game = new SimpleGame('canvas');
 *
 * // 第2步：创建游戏对象
 * // MovingBox是一个示例对象，会在画布中弹跳移动
 * const box = new MovingBox(100, 100);  // 在坐标(100, 100)创建方块
 *
 * // 第3步：将对象添加到游戏中
 * // 添加后，对象会被自动管理（更新和绘制）
 * game.addObject(box);
 *
 * // 第4步：启动游戏
 * // 开始游戏循环，对象开始移动和绘制
 * game.start();
 *
 * // 可选：停止游戏
 * // game.stop();
 *
 * // 可选：添加更多对象
 * // const box2 = new MovingBox(200, 200);
 * // game.addObject(box2);
 */

/* ========================================
   学习进阶建议
   ======================================== */

/**
 * 🎯 学习路径建议：
 *
 * 1. 【理解基础概念】
 *    - 运行test.html，观察移动方块的效果
 *    - 理解游戏循环的概念（update -> draw -> repeat）
 *    - 学习Canvas 2D API的基本用法
 *
 * 2. 【修改和实验】
 *    - 修改MovingBox的颜色、大小、速度
 *    - 尝试创建多个方块
 *    - 实验不同的边界检测逻辑
 *
 * 3. 【创建自定义对象】
 *    - 创建一个圆形对象（使用ctx.arc()）
 *    - 实现键盘控制的对象
 *    - 添加碰撞检测功能
 *
 * 4. 【进阶功能】
 *    - 实现简单的吃豆人角色
 *    - 添加地图系统
 *    - 实现游戏状态管理
 *
 * 5. 【完整游戏】
 *    - 学习完整版的game.js和index.js
 *    - 理解AI寻路算法
 *    - 实现完整的吃豆人游戏
 */

/**
 * 🔧 调试技巧：
 *
 * 1. 使用console.log()输出调试信息：
 *    MovingBox.prototype.update = function() {
 *        console.log('位置:', this.x, this.y);
 *        // ... 其他代码
 *    };
 *
 * 2. 在浏览器开发者工具中设置断点
 *
 * 3. 修改对象属性观察效果：
 *    box.speedX = 5;  // 改变速度
 *    box.color = '#00FF00';  // 改变颜色
 *
 * 4. 添加边界可视化：
 *    在draw方法中添加边框绘制
 */

/**
 * 📚 相关知识点：
 *
 * - JavaScript面向对象编程（构造函数、原型、继承）
 * - HTML5 Canvas 2D API
 * - requestAnimationFrame动画
 * - 游戏循环和状态管理
 * - 碰撞检测算法
 * - 事件处理和用户输入
 */
