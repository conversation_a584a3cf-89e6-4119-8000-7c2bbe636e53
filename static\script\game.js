'use strict';
/*!
 * Pacman - HTML5 Game
 * https://passer-by.com/pacman/
 *
 * Copyright (c) 2016-present, <PERSON><PERSON><PERSON><PERSON>
 * Released under the MIT License.
 * https://github.com/mumuy/pacman/blob/master/LICENSE
*/

/**
 * 小型游戏引擎
 *
 * 这是一个轻量级的2D游戏引擎，专为Canvas游戏开发设计
 * 主要功能包括：
 * - 游戏对象管理（Item类）
 * - 地图系统（Map类）
 * - 场景管理（Stage类）
 * - 动画循环控制
 * - 事件处理系统
 * - 寻路算法
 */

/* ========================================
   requestAnimationFrame 兼容性处理
   ======================================== */

// 为旧版浏览器提供Date.now()方法
// if (!Date.now) {
//     Date.now = function() {
//         return new Date().getTime();
//     };
// }

/**
 * requestAnimationFrame polyfill
 * 确保在所有浏览器中都能使用requestAnimationFrame进行动画
 * 如果浏览器不支持，则使用setTimeout模拟
 */
// (function() {
//     'use strict';

//     // 浏览器厂商前缀列表
//     var vendors = ['webkit', 'moz'];

//     // 尝试使用带厂商前缀的requestAnimationFrame
//     for (var i = 0; i < vendors.length && !window.requestAnimationFrame; ++i) {
//         var vp = vendors[i];
//         window.requestAnimationFrame = window[vp+'RequestAnimationFrame'];
//         window.cancelAnimationFrame = (window[vp+'CancelAnimationFrame'] || window[vp+'CancelRequestAnimationFrame']);
//     }

//     // 如果是iOS6或者浏览器不支持requestAnimationFrame，使用setTimeout替代
//     if (/iP(ad|hone|od).*OS 6/.test(window.navigator.userAgent) // iOS6存在bug
//     || !window.requestAnimationFrame || !window.cancelAnimationFrame) {
//         var lastTime = 0;

//         // 使用setTimeout模拟requestAnimationFrame，目标帧率约60fps（16ms间隔）
//         window.requestAnimationFrame = function(callback) {
//             var now = Date.now();
//             var nextTime = Math.max(lastTime + 16, now);  // 确保至少16ms间隔
//             return setTimeout(function() {
//                 callback(lastTime = nextTime);
//             }, nextTime - now);
//         };

//         // 使用clearTimeout模拟cancelAnimationFrame
//         window.cancelAnimationFrame = clearTimeout;
//     }
// }());

/* ========================================
   游戏引擎主类 (Game)
   ======================================== */

/**
 * 游戏引擎构造函数
 * @param {string} id - Canvas元素的ID
 * @param {Object} params - 游戏配置参数
 */
function Game(id, params) {
    var _ = this;  // 保存this引用，避免作用域问题

    // 默认游戏设置
    var settings = {
        width: 960,     // 画布宽度（像素）
        height: 640     // 画布高度（像素）
    };

    // 合并默认设置和用户参数
    Object.assign(_, settings, params);

    // 获取Canvas元素并设置尺寸
    var $canvas = document.getElementById(id);
    $canvas.width = _.width;
    $canvas.height = _.height;

    // 游戏引擎私有变量
    var _context = $canvas.getContext('2d');    // Canvas 2D渲染上下文
    var _stages = [];                           // 场景对象队列，存储所有游戏场景
    var _events = {};                           // 事件处理器集合
    var _index = 0;                             // 当前活动场景的索引
    var _hander;                                // 动画帧控制句柄

    /* ========================================
       游戏对象类 (Item)
       ======================================== */

    /**
     * 游戏对象构造函数
     * 用于创建游戏中的所有可视化对象（玩家、敌人、道具等）
     * @param {Object} params - 对象初始化参数
     */
    var Item = function(params) {
        this._params = params || {};        // 保存原始参数
        this._id = 0;                       // 对象唯一标识符
        this._stage = null;                 // 所属场景的引用

        // 对象默认属性设置
        this._settings = {
            // 基础属性
            x: 0,                           // 位置坐标：横坐标（像素）
            y: 0,                           // 位置坐标：纵坐标（像素）
            width: 20,                      // 对象宽度（像素）
            height: 20,                     // 对象高度（像素）

            // 对象类型和状态
            type: 0,                        // 对象类型：0=普通对象，1=玩家控制，2=AI控制
            color: '#F00',                  // 对象标识颜色
            status: 1,                      // 对象状态：0=未激活，1=正常，2=暂停，3=临时，4=异常
            orientation: 0,                 // 朝向：0=右，1=下，2=左，3=上
            speed: 0,                       // 移动速度（像素/帧）

            // 地图相关属性
            location: null,                 // 关联的地图对象引用
            coord: null,                    // 在地图中的坐标（网格坐标）
            path: [],                       // AI寻路结果路径数组
            vector: null,                   // 当前移动目标坐标

            // 动画和时间相关
            frames: 1,                      // 动画帧间隔（每N帧更新一次）
            times: 0,                       // 动画帧计数器
            timeout: 0,                     // 倒计时器（用于临时状态）
            control: {},                    // 控制指令缓存

            // 回调函数
            update: function() {},          // 每帧更新逻辑
            draw: function() {}             // 绘制函数
        };

        // 将默认设置和用户参数合并到对象实例
        Object.assign(this, this._settings, this._params);
    };
    /**
     * 为游戏对象绑定事件处理器
     * @param {string} eventType - 事件类型（如'click', 'mouseover'等）
     * @param {Function} callback - 事件回调函数
     */
    Item.prototype.bind = function(eventType, callback) {
        // 如果该事件类型还没有注册过，则初始化事件监听
        if (!_events[eventType]) {
            _events[eventType] = {};

            // 在Canvas上添加事件监听器
            $canvas.addEventListener(eventType, function(e) {
                var position = _.getPosition(e);  // 获取鼠标在Canvas中的位置

                // 遍历当前场景中的所有对象，检查鼠标是否在对象范围内
                _stages[_index].items.forEach(function(item) {
                    // 碰撞检测：检查鼠标位置是否在对象的矩形范围内
                    if (item.x <= position.x && position.x <= item.x + item.width &&
                        item.y <= position.y && position.y <= item.y + item.height) {

                        var key = 's' + _index + 'i' + item._id;  // 生成唯一的事件键
                        if (_events[eventType][key]) {
                            _events[eventType][key](e);  // 执行对象的事件回调
                        }
                    }
                });
                e.preventDefault();  // 阻止默认事件行为
            });
        }

        // 为当前对象注册事件回调，绑定正确的作用域
        _events[eventType]['s' + this._stage.index + 'i' + this._id] = callback.bind(this);
    };

    /* ========================================
       地图类 (Map)
       ======================================== */

    /**
     * 地图对象构造函数
     * 用于管理游戏地图数据、坐标转换和寻路算法
     * @param {Object} params - 地图初始化参数
     */
    var Map = function(params) {
        this._params = params || {};        // 保存原始参数
        this._id = 0;                       // 地图唯一标识符
        this._stage = null;                 // 所属场景的引用

        // 地图默认属性设置
        this._settings = {
            x: 0,                           // 地图在Canvas中的起始X坐标
            y: 0,                           // 地图在Canvas中的起始Y坐标
            size: 20,                       // 地图网格单元的像素大小
            data: [],                       // 地图数据二维数组
            x_length: 0,                    // 地图X轴方向的网格数量
            y_length: 0,                    // 地图Y轴方向的网格数量
            frames: 1,                      // 动画帧间隔
            times: 0,                       // 动画帧计数器
            cache: false,                   // 是否启用静态缓存（提高性能）
            update: function() {},          // 地图更新逻辑
            draw: function() {}             // 地图绘制函数
        };

        // 将默认设置和用户参数合并到地图实例
        Object.assign(this, this._settings, this._params);
    };
    /**
     * 获取地图指定坐标的值
     * @param {number} x - 网格X坐标
     * @param {number} y - 网格Y坐标
     * @returns {number} 地图数据值，如果坐标无效返回-1
     */
    Map.prototype.get = function(x, y) {
        // 检查坐标是否在有效范围内
        if (this.data[y] && typeof this.data[y][x] != 'undefined') {
            return this.data[y][x];
        }
        return -1;  // 无效坐标返回-1
    };

    /**
     * 设置地图指定坐标的值
     * @param {number} x - 网格X坐标
     * @param {number} y - 网格Y坐标
     * @param {number} value - 要设置的值
     */
    Map.prototype.set = function(x, y, value) {
        if (this.data[y]) {
            this.data[y][x] = value;
        }
    };

    /**
     * 将地图网格坐标转换为Canvas像素坐标
     * @param {number} cx - 网格X坐标
     * @param {number} cy - 网格Y坐标
     * @returns {Object} 包含x,y属性的像素坐标对象
     */
    Map.prototype.coord2position = function(cx, cy) {
        return {
            x: this.x + cx * this.size + this.size / 2,  // 转换为像素X坐标（网格中心）
            y: this.y + cy * this.size + this.size / 2   // 转换为像素Y坐标（网格中心）
        };
    };

    /**
     * 将Canvas像素坐标转换为地图网格坐标
     * @param {number} x - 像素X坐标
     * @param {number} y - 像素Y坐标
     * @returns {Object} 包含网格坐标和偏移量的对象
     */
    Map.prototype.position2coord = function(x, y) {
        // 计算相对于网格中心的偏移量
        var fx = Math.abs(x - this.x) % this.size - this.size / 2;
        var fy = Math.abs(y - this.y) % this.size - this.size / 2;

        return {
            x: Math.floor((x - this.x) / this.size),        // 网格X坐标
            y: Math.floor((y - this.y) / this.size),        // 网格Y坐标
            offset: Math.sqrt(fx * fx + fy * fy)            // 距离网格中心的偏移量
        };
    };
    
    //寻址算法
    Map.prototype.finder = function(params){
        var defaults = {
            map:null,
            start:{},
            end:{},
            type:'path'
        };
        var options = Object.assign({},defaults,params);
        if(options.map[options.start.y][options.start.x]||options.map[options.end.y][options.end.x]){ //当起点或终点设置在墙上
            return [];
        }
        var finded = false;
        var result = [];
        var y_length  = options.map.length;
        var x_length = options.map[0].length;
        var steps = Array(y_length).fill(0).map(()=>Array(x_length).fill(0));     //步骤的映射
        var _getValue = function(x,y){  //获取地图上的值
            if(options.map[y]&&typeof options.map[y][x]!='undefined'){
                return options.map[y][x];
            }
            return -1;
        };
        var _next = function(to){ //判定是否可走,可走放入列表
            var value = _getValue(to.x,to.y);
            if(value<1){
                if(value==-1){
                    to.x = (to.x+x_length)%x_length;
                    to.y = (to.y+y_length)%y_length;
                    to.change = 1;
                }
                if(!steps[to.y][to.x]){
                    result.push(to);
                }
            }
        };
        var _render = function(list){//找线路
            var new_list = [];
            var next = function(from,to){
                var value = _getValue(to.x,to.y);
                if(value<1){	//当前点是否可以走
                    if(value==-1){
                        to.x = (to.x+x_length)%x_length;
                        to.y = (to.y+y_length)%y_length;
                        to.change = 1;
                    }
                    if(to.x==options.end.x&&to.y==options.end.y){
                        steps[to.y][to.x] = from;
                        finded = true;
                    }else if(!steps[to.y][to.x]){
                        steps[to.y][to.x] = from;
                        new_list.push(to);
                    }
                }
            };
            list.forEach(function(current){
				next(current,{y:current.y+1,x:current.x});
                next(current,{y:current.y,x:current.x+1});
                next(current,{y:current.y-1,x:current.x});
                next(current,{y:current.y,x:current.x-1});
            });
            if(!finded&&new_list.length){
                _render(new_list);
            }
        };
        _render([options.start]);
        if(finded){
            var current=options.end;
            if(options.type=='path'){
                while(current.x!=options.start.x||current.y!=options.start.y){
                    result.unshift(current);
                    current=steps[current.y][current.x];
                }
            }else if(options.type=='next'){
                _next({x:current.x+1,y:current.y});
                _next({x:current.x,y:current.y+1});
                _next({x:current.x-1,y:current.y});
                _next({x:current.x,y:current.y-1});
            }
        }
        return result;
    };
    //布景对象构造器
    var Stage = function(params){
        this._params = params||{};
        this._settings = {
            index:0,                        //布景索引
            status:0,						//布景状态,0表示未激活/结束,1表示正常,2表示暂停,3表示临时状态
            maps:[],						//地图队列
            audio:[],						//音频资源
            images:[],						//图片资源
            items:[],						//对象队列
            timeout:0,						//倒计时(用于过程动画状态判断)
            update:function(){}				//嗅探,处理布局下不同对象的相对关系
        };
        Object.assign(this,this._settings,this._params);
    };
    //添加对象
    Stage.prototype.createItem = function(options){
        var item = new Item(options);
        //动态属性
        if(item.location){
            Object.assign(item,item.location.coord2position(item.coord.x,item.coord.y));
        }
        //关系绑定
        item._stage = this;
        item._id = this.items.length;
        this.items.push(item);
        return item;
    };
    //重置物体位置
    Stage.prototype.resetItems = function(){
        this.status = 1;
        this.items.forEach(function(item,index){
            Object.assign(item,item._settings,item._params);
            if(item.location){
                Object.assign(item,item.location.coord2position(item.coord.x,item.coord.y));
            }
        });
    };
    //获取对象列表
    Stage.prototype.getItemsByType = function(type){
        return this.items.filter(function(item){
	    return item.type == type;
        });
    };
    //添加地图
    Stage.prototype.createMap = function(options){
        var map = new Map(options);
        //动态属性
        map.data = JSON.parse(JSON.stringify(map._params.data));
        map.y_length = map.data.length;
        map.x_length = map.data[0].length;
        map.imageData = null;
        //关系绑定
        map._stage = this;
        map._id = this.maps.length;
        this.maps.push(map);
        return map;
    };
    //重置地图
    Stage.prototype.resetMaps = function(){
        this.status = 1;
        this.maps.forEach(function(map){
            Object.assign(map,map._settings,map._params);
            map.data = JSON.parse(JSON.stringify(map._params.data));
            map.y_length = map.data.length;
            map.x_length = map.data[0].length;
            map.imageData = null;
        });
    };
    //重置
    Stage.prototype.reset = function(){
        Object.assign(this,this._settings,this._params);
        this.resetItems();
        this.resetMaps();
    };
    //绑定事件
    Stage.prototype.bind = function(eventType,callback){
        if(!_events[eventType]){
            _events[eventType] = {};
            window.addEventListener(eventType,function(e){
                var key = 's' + _index;
                if(_events[eventType][key]){
                    _events[eventType][key](e);
                }
                e.preventDefault();
            });
        }
        _events[eventType]['s'+this.index] = callback.bind(this);	//绑定事件作用域
    };
    //动画开始
    this.start = function() {
        var f = 0;		//帧数计算
        var timestamp = (new Date()).getTime();
        var fn = function(){
            var now = (new Date()).getTime();
            if(now-timestamp<16){   // 限频，防止高刷屏幕动画过快
                _hander = requestAnimationFrame(fn);
                return false;
            }
            timestamp = now;
            var stage = _stages[_index];
            _context.clearRect(0,0,_.width,_.height);		//清除画布
            _context.fillStyle = '#000000';
            _context.fillRect(0,0,_.width,_.height);
            f++;
            if(stage.timeout){
                stage.timeout--;
            }
            if(stage.update()!=false){		            //update返回false,则不绘制
                stage.maps.forEach(function(map){
                    if(!(f%map.frames)){
                        map.times = f/map.frames;		//计数器
                    }
                    if(map.cache){
                        if(!map.imageData){
                            _context.save();
                            map.draw(_context);
                            map.imageData = _context.getImageData(0,0,_.width,_.height);
                            _context.restore();
                        }else{
                            _context.putImageData(map.imageData,0,0);
                        }
                    }else{
                    	map.update();
                        map.draw(_context);
                    }
                });
                stage.items.forEach(function(item){
                    if(!(f%item.frames)){
                        item.times = f/item.frames;		   //计数器
                    }
                    if(stage.status==1&&item.status!=2){  	//对象及布景状态都不处于暂停状态
                        if(item.location){
                            item.coord = item.location.position2coord(item.x,item.y);
                        }
                        if(item.timeout){
                            item.timeout--;
                        }
                        item.update();
                    }
                    item.draw(_context);
                });
            }
            _hander = requestAnimationFrame(fn);
        };
        _hander = requestAnimationFrame(fn);
    };
    //动画结束
    this.stop = function(){
        _hander&&cancelAnimationFrame(_hander);
    };
    //事件坐标
    this.getPosition = function(e){
        var box = $canvas.getBoundingClientRect();
        return {
            x:e.clientX-box.left*(_.width/box.width),
            y:e.clientY-box.top*(_.height/box.height)
        };
    }
    //创建布景
    this.createStage = function(options){
        var stage = new Stage(options);
        stage.index = _stages.length;
        _stages.push(stage);
        return stage;
    };
    //指定布景
    this.setStage = function(index){
        _stages[_index].status = 0;
        _index = index;
        _stages[_index].status = 1;
        _stages[_index].reset(); //重置
        return _stages[_index];
    };
    //下个布景
    this.nextStage = function(){
        if(_index<_stages.length-1){
            return this.setStage(++_index);
        }else{
            throw new Error('unfound new stage.');
        }
    };
    //获取布景列表
    this.getStages = function(){
        return _stages;
    };
    //初始化游戏引擎
    this.init = function(){
        _index = 0;
        this.start();
    };
}
