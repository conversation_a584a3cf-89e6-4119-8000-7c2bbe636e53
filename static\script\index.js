/*!
 * Pacman - HTML5 Game
 * https://passer-by.com/pacman/
 *
 * Copyright (c) 2016-present, <PERSON><PERSON><PERSON><PERSON>
 * Released under the MIT License.
 * https://github.com/mumuy/pacman/blob/master/LICENSE
*/

/**
 * 吃豆人游戏主程序
 *
 * 这个文件包含了游戏的核心业务逻辑：
 * - 关卡配置数据
 * - 游戏角色行为逻辑
 * - 碰撞检测系统
 * - AI寻路算法应用
 * - 游戏状态管理
 * - 用户输入处理
 */
(function(){
	/* ========================================
	   游戏配置数据
	   ======================================== */

	/**
	 * 关卡配置数组
	 * 每个关卡包含地图数据、墙体颜色和能量豆位置
	 */
	var _COIGIG = [
		{   // 第1关配置
			'map': [    // 地图数据：二维数组，0=通道，1=墙体，2=幽灵区域
				[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],
				[1,0,0,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,0,0,1],
				[1,0,1,1,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,1,1,0,1],
				[1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,0,0,0,0,0,1,1,0,0,0,0,1,1,0,0,0,0,1,1,0,0,0,0,0,0,1],
				[1,1,1,1,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,1,1,1,1],
				[1,1,1,1,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,1,1,1,1],
				[1,1,1,1,1,1,0,1,1,0,0,0,0,0,0,0,0,0,0,1,1,0,1,1,1,1,1,1],
				[1,1,1,1,1,1,0,1,1,0,1,1,1,2,2,1,1,1,0,1,1,0,1,1,1,1,1,1],
				[1,1,1,1,1,1,0,1,1,0,1,2,2,2,2,2,2,1,0,1,1,0,1,1,1,1,1,1],
				[0,0,0,0,0,0,0,0,0,0,1,2,2,2,2,2,2,1,0,0,0,0,0,0,0,0,0,0],
				[1,1,1,1,1,1,0,1,1,0,1,2,2,2,2,2,2,1,0,1,1,0,1,1,1,1,1,1],
				[1,1,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,1,1],
				[1,1,1,1,1,1,0,1,1,0,0,0,0,0,0,0,0,0,0,1,1,0,1,1,1,1,1,1],
				[1,1,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,1,1],
				[1,1,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,1,1],
				[1,0,0,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,0,0,1],
				[1,0,1,1,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,1,1,0,1],
				[1,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,1],
				[1,1,1,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,0,1,1,1],
				[1,0,0,0,0,0,0,1,1,0,0,0,0,1,1,0,0,0,0,1,1,0,0,0,0,0,0,1],
				[1,0,1,1,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,1],
				[1,0,1,1,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,1],
				[1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1],
				[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1]
			],
			'wall_color':'#09f',
			'goods':{		//能量豆
				'1,3':1,
				'26,3':1,
				'1,23':1,
				'26,23':1
			}
		},
		{				//第2关
			'map':[		//地图数据
				[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],
				[1,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1],
				[1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1],
				[0,0,0,0,1,1,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,1,1,0,0,0,0],
				[1,1,1,0,1,1,1,1,1,0,1,1,1,1,1,1,1,1,0,1,1,1,1,1,0,1,1,1],
				[1,1,1,0,1,1,1,1,1,0,1,1,1,1,1,1,1,1,0,1,1,1,1,1,0,1,1,1],
				[1,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1],
				[1,1,1,0,1,1,1,1,1,0,1,1,1,2,2,1,1,1,0,1,1,1,1,1,0,1,1,1],
				[1,1,1,0,1,1,1,1,1,0,1,2,2,2,2,2,2,1,0,1,1,1,1,1,0,1,1,1],
				[1,1,1,0,1,1,0,0,0,0,1,2,2,2,2,2,2,1,0,0,0,0,1,1,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,0,1,2,2,2,2,2,2,1,0,1,1,0,1,1,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,0,1,1,1],
				[0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0],
				[1,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,1],
				[1,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,1],
				[1,1,1,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,1,1,1],
				[1,1,1,0,1,1,1,1,1,0,1,1,1,1,1,1,1,1,0,1,1,1,1,1,0,1,1,1],
				[1,1,1,0,1,1,1,1,1,0,1,1,1,1,1,1,1,1,0,1,1,1,1,1,0,1,1,1],
				[1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1],
				[1,0,1,1,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,0,0,0,0,1,1,0,0,0,0,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1],
				[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1]
			],
			'wall_color':'#FF5983',
			'goods':{		//能量豆
				'1,2':1,
				'26,2':1,
				'1,27':1,
				'26,27':1
			}
		},
		{				//第3关
			'map':[		//地图数据
				[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],
				[1,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,1],
				[1,0,1,1,1,1,1,1,1,0,1,1,0,1,1,0,1,1,0,1,1,1,1,1,1,1,0,1],
				[1,0,1,1,1,1,1,1,1,0,1,1,0,1,1,0,1,1,0,1,1,1,1,1,1,1,0,1],
				[1,0,1,1,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,1,1,0,1],
				[1,0,1,1,0,1,1,0,1,1,1,1,0,1,1,0,1,1,1,1,0,1,1,0,1,1,0,1],
				[1,0,0,0,0,1,1,0,1,1,1,1,0,1,1,0,1,1,1,1,0,1,1,0,0,0,0,1],
				[1,1,1,1,0,1,1,0,1,1,1,1,0,1,1,0,1,1,1,1,0,1,1,0,1,1,1,1],
				[1,1,1,1,0,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,0,1,1,1,1],
				[0,0,0,0,0,1,1,1,1,0,1,1,1,1,1,1,1,1,0,1,1,1,1,0,0,0,0,0],
				[1,0,1,1,0,1,1,1,1,0,1,1,1,1,1,1,1,1,0,1,1,1,1,0,1,1,0,1],
				[1,0,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,1,2,2,1,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,2,2,2,2,2,2,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,0,0,0,0,0,1,1,0,1,2,2,2,2,2,2,1,0,1,1,0,0,0,0,0,0,1],
				[1,0,1,1,0,1,1,1,1,0,1,2,2,2,2,2,2,1,0,1,1,1,1,0,1,1,0,1],
				[1,0,1,1,0,1,1,1,1,0,1,1,1,1,1,1,1,1,0,1,1,1,1,0,1,1,0,1],
				[1,0,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,1,1,0,1],
				[1,0,0,0,0,0,0,1,1,0,0,0,0,1,1,0,0,0,0,1,1,0,0,0,0,0,0,1],
				[1,1,1,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,0,1,1,1],
				[1,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,1],
				[1,0,1,1,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,1,1,0,1],
				[1,0,0,0,0,0,0,1,1,0,0,0,0,1,1,0,0,0,0,1,1,0,0,0,0,0,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,1],
				[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1]
			],
			'wall_color':'#E08031',
			'goods':{		//能量豆
				'1,2':1,
				'26,2':1,
				'1,23':1,
				'26,23':1
			}
		},
		{				//第4关
			'map':[		//地图数据
				[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],
				[1,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,0,0,1,1,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,1,1,0,0,0,1],
				[1,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,1],
				[0,0,0,0,0,0,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,0,0,0,0,0,0],
				[1,1,1,0,1,1,0,1,1,0,1,1,0,0,0,0,1,1,0,1,1,0,1,1,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,0,1,1,1],
				[1,0,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,0,0,0,1],
				[1,0,1,1,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,1,2,2,1,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,0,0,0,0,0,1,1,0,1,2,2,2,2,2,2,1,0,1,1,0,0,0,0,0,0,1],
				[1,1,1,0,1,1,1,1,1,0,1,2,2,2,2,2,2,1,0,1,1,1,1,1,0,1,1,1],
				[1,1,1,0,1,1,1,1,1,0,1,2,2,2,2,2,2,1,0,1,1,1,1,1,0,1,1,1],
				[1,0,0,0,0,0,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,0,0,0,0,0,1],
				[1,0,1,1,1,1,0,1,1,0,0,0,0,0,0,0,0,0,0,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,0,0,0,1],
				[1,1,1,0,1,1,0,1,1,0,0,0,0,1,1,0,0,0,0,1,1,0,1,1,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,1],
				[0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0],
				[1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1],
				[1,0,0,0,1,1,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,1,1,0,0,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,1],
				[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1]
			],
			'wall_color':'#37C6C0',
			'goods':{		//能量豆
				'1,3':1,
				'26,3':1,
				'1,28':1,
				'26,28':1
			}
		},
		{				//第5关
			'map':[		//地图数据
				[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],
				[1,0,0,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,0,0,1],
				[1,0,1,1,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,1,1,0,1],
				[1,0,0,0,0,0,0,0,0,0,1,1,0,1,1,0,1,1,0,0,0,0,0,0,0,0,0,1],
				[1,1,1,0,1,1,1,1,1,0,1,1,0,0,0,0,1,1,0,1,1,1,1,1,0,1,1,1],
				[1,1,1,0,1,1,1,1,1,0,1,1,0,1,1,0,1,1,0,1,1,1,1,1,0,1,1,1],
				[1,0,0,0,0,0,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,0,0,0,0,0,1],
				[1,0,1,1,1,1,0,1,1,0,0,0,0,1,1,0,0,0,0,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,0,0,0,1],
				[1,1,1,0,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,0,1,1,1,2,2,1,1,1,0,1,1,0,1,1,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,0,1,2,2,2,2,2,2,1,0,1,1,0,1,1,0,1,1,1],
				[0,0,0,0,0,0,0,1,1,0,1,2,2,2,2,2,2,1,0,1,1,0,0,0,0,0,0,0],
				[1,1,1,0,1,1,1,1,1,0,1,2,2,2,2,2,2,1,0,1,1,1,1,1,0,1,1,1],
				[1,1,1,0,1,1,1,1,1,0,1,1,1,1,1,1,1,1,0,1,1,1,1,1,0,1,1,1],
				[1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,0,0,0,1,1,0,0,0,0,1,1,0,0,0,0,1,1,0,0,0,1,1,0,1],
				[1,0,1,1,0,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,0,1,1,0,1],
				[1,0,1,1,0,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,0,1,1,0,1],
				[1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1],
				[1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1],
				[1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1],
				[1,0,0,0,0,0,0,0,1,1,0,0,0,1,1,0,0,0,1,1,0,0,0,0,0,0,0,1],
				[1,0,1,1,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,1],
				[1,0,1,1,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,1],
				[1,0,0,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,0,0,1],
				[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1]
			],
			'wall_color':'#5ED5D1',
			'goods':{		//能量豆
				'1,3':1,
				'26,3':1,
				'1,27':1,
				'26,27':1
			}
		},
		{				//第6关
			'map':[		//地图数据
				[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],
				[1,0,0,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,0,0,1],
				[1,0,1,1,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,1,1,0,1],
				[1,0,0,0,1,1,0,0,0,0,1,1,0,1,1,0,1,1,0,0,0,0,1,1,0,0,0,1],
				[1,1,1,0,1,1,0,1,1,0,1,1,0,0,0,0,1,1,0,1,1,0,1,1,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,1],
				[1,1,1,0,0,0,0,1,1,0,0,0,0,1,1,0,0,0,0,1,1,0,0,0,0,1,1,1],
				[1,1,1,0,1,1,1,1,1,0,1,1,1,1,1,1,1,1,0,1,1,1,1,1,0,1,1,1],
				[1,1,1,0,1,1,1,1,1,0,1,1,1,1,1,1,1,1,0,1,1,1,1,1,0,1,1,1],
				[1,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,0,1,1,1,2,2,1,1,1,0,1,1,0,1,1,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,0,1,2,2,2,2,2,2,1,0,1,1,0,1,1,0,1,1,1],
				[0,0,0,0,1,1,0,1,1,0,1,2,2,2,2,2,2,1,0,1,1,0,1,1,0,0,0,0],
				[1,1,1,1,1,1,0,1,1,0,1,2,2,2,2,2,2,1,0,1,1,0,1,1,1,1,1,1],
				[1,1,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,1,1],
				[0,0,0,0,1,1,0,1,1,0,0,0,0,0,0,0,0,0,0,1,1,0,1,1,0,0,0,0],
				[1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1],
				[1,1,1,0,0,0,0,1,1,0,0,0,0,1,1,0,0,0,0,1,1,0,0,0,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,0,1,1,1],
				[1,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,1],
				[1,0,1,1,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,1,1,0,1],
				[1,0,0,0,0,0,0,1,1,0,0,0,0,1,1,0,0,0,0,1,1,0,0,0,0,0,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1],
				[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1]
			],
			'wall_color':'#7E884F',
			'goods':{		//能量豆
				'1,3':1,
				'26,3':1,
				'1,28':1,
				'26,28':1
			}
		},
		{				//第7关
			'map':[		//地图数据
				[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],
				[1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1],
				[1,0,1,1,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,1,1,0,1],
				[1,0,0,0,0,0,0,0,0,0,1,1,0,1,1,0,1,1,0,0,0,0,0,0,0,0,0,1],
				[1,1,1,0,1,1,1,1,1,0,1,1,0,1,1,0,1,1,0,1,1,1,1,1,0,1,1,1],
				[1,1,1,0,1,1,1,1,1,0,1,1,0,1,1,0,1,1,0,1,1,1,1,1,0,1,1,1],
				[0,0,0,0,0,0,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,0,0,0,0,0,0],
				[1,1,1,0,1,1,0,1,1,0,0,0,0,0,0,0,0,0,0,1,1,0,1,1,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,0,1,1,1],
				[1,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,1],
				[1,0,1,1,1,1,1,1,1,0,1,1,1,2,2,1,1,1,0,1,1,1,1,1,1,1,0,1],
				[1,0,1,1,1,1,1,1,1,0,1,2,2,2,2,2,2,1,0,1,1,1,1,1,1,1,0,1],
				[1,0,0,0,0,0,0,0,0,0,1,2,2,2,2,2,2,1,0,0,0,0,0,0,0,0,0,1],
				[1,0,1,1,0,1,1,1,1,0,1,2,2,2,2,2,2,1,0,1,1,1,1,0,1,1,0,1],
				[1,0,1,1,0,1,1,1,1,0,1,1,1,1,1,1,1,1,0,1,1,1,1,0,1,1,0,1],
				[1,0,1,1,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,0,0,0,0,0,0,1,1,0,1,1,0,1,1,0,0,0,0,0,0,1,1,0,1],
				[1,0,1,1,0,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,0,1,1,0,1],
				[1,0,1,1,0,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,0,1,1,0,1],
				[1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1],
				[1,0,1,1,0,1,1,1,1,0,1,1,1,1,1,1,1,1,0,1,1,1,1,0,1,1,0,1],
				[1,0,1,1,0,1,1,1,1,0,1,1,1,1,1,1,1,1,0,1,1,1,1,0,1,1,0,1],
				[1,0,1,1,0,0,0,1,1,0,0,0,0,1,1,0,0,0,0,1,1,0,0,0,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,1],
				[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1]
			],
			'wall_color':'#C9C',
			'goods':{		//能量豆
				'1,3':1,
				'26,3':1,
				'1,24':1,
				'26,24':1
			}
		},
		{				//第8关
			'map':[		//地图数据
				[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],
				[1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,0,0,0,1,1,0,0,0,0,1,1,0,0,0,0,1,1,0,0,0,1,1,0,1],
				[1,0,1,1,0,1,1,1,1,0,1,1,0,1,1,0,1,1,0,1,1,1,1,0,1,1,0,1],
				[1,0,1,1,0,1,1,1,1,0,1,1,0,1,1,0,1,1,0,1,1,1,1,0,1,1,0,1],
				[0,0,0,0,0,0,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,0,0,0,0,0,0],
				[1,0,1,1,1,1,0,1,1,0,1,1,0,0,0,0,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,0,0,0,1],
				[1,1,1,0,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,0,1,1,1,2,2,1,1,1,0,1,1,0,1,1,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,0,1,2,2,2,2,2,2,1,0,1,1,0,1,1,0,1,1,1],
				[1,1,1,0,0,0,0,1,1,0,1,2,2,2,2,2,2,1,0,1,1,0,0,0,0,1,1,1],
				[1,1,1,0,1,1,1,1,1,0,1,2,2,2,2,2,2,1,0,1,1,1,1,1,0,1,1,1],
				[1,1,1,0,1,1,1,1,1,0,1,1,1,1,1,1,1,1,0,1,1,1,1,1,0,1,1,1],
				[1,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1],
				[1,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,1],
				[1,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,1],
				[0,0,0,0,1,1,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,1,1,0,0,0,0],
				[1,1,1,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,0,1,1,1],
				[1,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,1],
				[1,0,1,1,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,1,1,0,1],
				[1,0,1,1,0,1,1,1,1,1,0,1,1,1,1,1,1,0,1,1,1,1,1,0,1,1,0,1],
				[1,0,1,1,0,1,1,1,1,1,0,1,1,1,1,1,1,0,1,1,1,1,1,0,1,1,0,1],
				[1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1],
				[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1]
			],
			'wall_color':'#EB3F2F',
			'goods':{		//能量豆
				'1,4':1,
				'26,4':1,
				'1,25':1,
				'26,25':1
			}
		},
		{				//第9关
			'map':[		//地图数据
				[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],
				[1,0,0,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,0,0,1],
				[1,0,1,1,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,0,0,0,0,0,0,1,1,0,1,1,0,1,1,0,0,0,0,0,0,1,1,0,1],
				[1,0,1,1,0,1,1,1,1,0,1,1,0,0,0,0,1,1,0,1,1,1,1,0,1,1,0,1],
				[1,0,1,1,0,1,1,1,1,0,1,1,0,1,1,0,1,1,0,1,1,1,1,0,1,1,0,1],
				[1,0,0,0,0,0,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,0,0,0,0,0,1],
				[1,0,1,1,1,1,0,1,1,0,0,0,0,1,1,0,0,0,0,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,0,0,0,1],
				[1,1,1,0,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,0,1,1,1,2,2,1,1,1,0,1,1,0,1,1,0,1,1,1],
				[1,1,1,0,0,0,0,1,1,0,1,2,2,2,2,2,2,1,0,1,1,0,0,0,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,0,1,2,2,2,2,2,2,1,0,1,1,0,1,1,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,0,1,2,2,2,2,2,2,1,0,1,1,0,1,1,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,0,1,1,1],
				[1,0,0,0,1,1,0,1,1,0,0,0,0,0,0,0,0,0,0,1,1,0,1,1,0,0,0,1],
				[1,0,1,1,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,1,1,0,1],
				[1,0,0,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,0,0,1],
				[1,1,1,0,1,1,1,1,1,0,1,1,1,1,1,1,1,1,0,1,1,1,1,1,0,1,1,1],
				[1,1,1,0,1,1,1,1,1,0,1,1,1,1,1,1,1,1,0,1,1,1,1,1,0,1,1,1],
				[0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0],
				[1,1,1,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,0,1,1,1],
				[1,0,0,0,1,1,0,0,0,0,1,1,0,0,0,0,1,1,0,0,0,0,1,1,0,0,0,1],
				[1,0,1,1,1,1,1,1,1,0,1,1,0,1,1,0,1,1,0,1,1,1,1,1,1,1,0,1],
				[1,0,1,1,1,1,1,1,1,0,1,1,0,1,1,0,1,1,0,1,1,1,1,1,1,1,0,1],
				[1,0,0,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,0,0,1],
				[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1]
			],
			'wall_color':'#2E68AA',
			'goods':{		//能量豆
				'1,6':1,
				'26,6':1,
				'1,27':1,
				'26,27':1
			}
		},
		{				//第10关
			'map':[		//地图数据
				[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],
				[1,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,0,0,0,0,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,0,0,0,0,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,0,0,0,0,0,1],
				[1,0,1,1,0,1,1,1,1,0,1,1,0,1,1,0,1,1,0,1,1,1,1,0,1,1,0,1],
				[1,0,1,1,0,1,1,1,1,0,1,1,0,1,1,0,1,1,0,1,1,1,1,0,1,1,0,1],
				[1,0,1,1,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,1],
				[1,1,1,0,1,1,1,1,1,0,1,1,1,2,2,1,1,1,0,1,1,1,1,1,0,1,1,1],
				[1,1,1,0,1,1,1,1,1,0,1,2,2,2,2,2,2,1,0,1,1,1,1,1,0,1,1,1],
				[1,1,1,0,0,0,0,1,1,0,1,2,2,2,2,2,2,1,0,1,1,0,0,0,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,0,1,2,2,2,2,2,2,1,0,1,1,0,1,1,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,0,1,1,1],
				[1,1,1,0,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,0,1,1,1],
				[1,1,1,0,1,1,1,1,1,0,1,1,1,1,1,1,1,1,0,1,1,1,1,1,0,1,1,1],
				[1,1,1,0,1,1,1,1,1,0,1,1,1,1,1,1,1,1,0,1,1,1,1,1,0,1,1,1],
				[1,1,1,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,1,1,1],
				[1,1,1,0,1,1,1,1,1,0,1,1,0,1,1,0,1,1,0,1,1,1,1,1,0,1,1,1],
				[1,1,1,0,1,1,1,1,1,0,1,1,0,1,1,0,1,1,0,1,1,1,1,1,0,1,1,1],
				[0,0,0,0,0,0,0,1,1,0,1,1,0,0,0,0,1,1,0,1,1,0,0,0,0,0,0,0],
				[1,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,1],
				[1,0,0,0,1,1,0,1,1,0,0,0,0,1,1,0,0,0,0,1,1,0,1,1,0,0,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1],
				[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1]
			],
			'wall_color':'#C1194E',
			'goods':{		//能量豆
				'1,4':1,
				'26,4':1,
				'1,28':1,
				'26,28':1
			}
		},
		{				//第11关
			'map':[		//地图数据
				[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],
				[1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1],
				[1,0,1,1,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,1],
				[1,0,1,1,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,1],
				[1,0,0,0,0,0,0,1,1,0,0,0,0,1,1,0,0,0,0,1,1,0,0,0,0,0,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,0,1],
				[1,0,1,1,0,1,1,1,1,0,1,1,1,1,1,1,1,1,0,1,1,1,1,0,1,1,0,1],
				[1,0,1,1,0,1,1,1,1,0,1,1,1,1,1,1,1,1,0,1,1,1,1,0,1,1,0,1],
				[1,0,0,0,0,0,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,0,0,0,0,0,1],
				[1,0,1,1,1,1,0,1,1,0,0,0,0,0,0,0,0,0,0,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,1,2,2,1,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,0,0,1,1,0,0,0,0,1,2,2,2,2,2,2,1,0,0,0,0,1,1,0,0,0,1],
				[1,1,1,0,1,1,0,1,1,0,1,2,2,2,2,2,2,1,0,1,1,0,1,1,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,0,1,2,2,2,2,2,2,1,0,1,1,0,1,1,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,0,1,1,1],
				[1,1,1,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,1,1,1],
				[1,1,1,0,1,1,1,1,1,0,1,1,1,1,1,1,1,1,0,1,1,1,1,1,0,1,1,1],
				[1,1,1,0,1,1,1,1,1,0,1,1,1,1,1,1,1,1,0,1,1,1,1,1,0,1,1,1],
				[1,1,1,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,1,1,1],
				[1,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,1],
				[1,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,1],
				[0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0],
				[1,1,1,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,0,1,1,1],
				[1,0,0,0,1,1,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,1,1,0,0,0,1],
				[1,0,1,1,1,1,1,1,1,0,1,1,0,1,1,0,1,1,0,1,1,1,1,1,1,1,0,1],
				[1,0,1,1,1,1,1,1,1,0,1,1,0,1,1,0,1,1,0,1,1,1,1,1,1,1,0,1],
				[1,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,1],
				[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1]
			],
			'wall_color':'#56A36C',
			'goods':{		//能量豆
				'1,3':1,
				'26,3':1,
				'1,28':1,
				'26,28':1
			}
		},
		{				//第12关
			'map':[		//地图数据
				[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],
				[1,0,0,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,0,0,1],
				[1,0,1,1,0,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,0,1,1,0,1],
				[1,0,1,1,0,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,0,1,1,0,1],
				[1,0,1,1,0,0,0,0,0,0,1,1,0,1,1,0,1,1,0,0,0,0,0,0,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,0,0,0,0,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,0,0,0,0,0,1,1,0,1,1,0,1,1,0,1,1,0,1,1,0,0,0,0,0,0,1],
				[1,1,1,0,1,1,1,1,1,0,0,0,0,1,1,0,0,0,0,1,1,1,1,1,0,1,1,1],
				[1,1,1,0,1,1,1,1,1,0,1,1,1,1,1,1,1,1,0,1,1,1,1,1,0,1,1,1],
				[1,1,1,0,0,0,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,0,0,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,0,0,0,0,0,0,0,0,0,0,1,1,0,1,1,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,0,1,1,1,2,2,1,1,1,0,1,1,0,1,1,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,0,1,2,2,2,2,2,2,1,0,1,1,0,1,1,0,1,1,1],
				[0,0,0,0,1,1,0,0,0,0,1,2,2,2,2,2,2,1,0,0,0,0,1,1,0,0,0,0],
				[1,1,1,1,1,1,1,1,1,0,1,2,2,2,2,2,2,1,0,1,1,1,1,1,1,1,1,1],
				[1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,1,1,1,0,1,1,1,1,1,1,1,1,1],
				[0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0],
				[1,1,1,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,0,1,1,1],
				[1,1,1,0,0,0,0,1,1,0,0,0,0,1,1,0,0,0,0,1,1,0,0,0,0,1,1,1],
				[1,1,1,0,1,1,1,1,1,0,1,1,0,1,1,0,1,1,0,1,1,1,1,1,0,1,1,1],
				[1,1,1,0,1,1,1,1,1,0,1,1,0,1,1,0,1,1,0,1,1,1,1,1,0,1,1,1],
				[1,1,1,0,0,0,0,0,0,0,1,1,0,0,0,0,1,1,0,0,0,0,0,0,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1],
				[1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1,1,1,0,1,1,0,1,1,1],
				[1,0,0,0,1,1,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,1,1,0,0,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,0,1],
				[1,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,1,1,0,0,0,0,0,0,1],
				[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1]
			],
			'wall_color':'#9966CC',     // 第12关墙体颜色：紫色
			'goods':{                   // 能量豆位置配置
				'1,4':1,                // 左上角能量豆
				'26,4':1,               // 右上角能量豆
				'1,27':1,               // 左下角能量豆
				'26,27':1               // 右下角能量豆
			}
		}
	];

	/* ========================================
	   游戏全局变量
	   ======================================== */

	// 幽灵颜色配置：红、橙、青、粉
	_COLOR = ['#F00','#F93','#0CF','#F9C'],

	// 方向向量：用于计算移动方向的余弦值（右、下、左、上）
	_COS = [1, 0, -1, 0],

	// 方向向量：用于计算移动方向的正弦值（右、下、左、上）
	_SIN = [0, 1, 0, -1],

	// 玩家生命值
	_LIFE = 5,

	// 玩家当前得分
	_SCORE = 0;

	/* ========================================
	   游戏引擎初始化
	   ======================================== */

	// 创建游戏引擎实例，绑定到id为'canvas'的Canvas元素
	var game = new Game('canvas');
	/* ========================================
	   游戏启动页面
	   ======================================== */

	/**
	 * 创建游戏启动页面
	 * 包含游戏Logo、标题、操作提示和版权信息
	 */
	(function(){
		// 创建启动页面场景
		var stage = game.createStage();

		/**
		 * 游戏Logo：动画吃豆人图标
		 * 使用Canvas绘制API创建经典的吃豆人形象
		 */
		stage.createItem({
			x: game.width / 2,          // 水平居中
			y: game.height * 0.35,      // 垂直位置：35%处
			width: 100,                 // Logo宽度
			height: 100,                // Logo高度
			frames: 3,                  // 动画帧间隔
			draw: function(context) {
				// 计算张嘴动画的角度变化
				var t = Math.abs(5 - this.times % 10);

				// 绘制吃豆人主体（黄色圆形，带张嘴动画）
				context.fillStyle = '#FFE600';  // 经典黄色
				context.beginPath();
				// 绘制扇形，模拟张嘴效果
				context.arc(this.x, this.y, this.width / 2,
					t * 0.04 * Math.PI,         // 起始角度
					(2 - t * 0.04) * Math.PI,   // 结束角度
					false);
				context.lineTo(this.x, this.y);  // 连接到中心点
				context.closePath();
				context.fill();

				// 绘制眼睛（黑色小圆）
				context.fillStyle = '#000';
				context.beginPath();
				context.arc(this.x + 5, this.y - 27, 7, 0, 2 * Math.PI, false);
				context.closePath();
				context.fill();
			}
		});

		/**
		 * 游戏标题文字
		 */
		stage.createItem({
			x: game.width / 2,          // 水平居中
			y: game.height * 0.5,       // 垂直位置：50%处
			draw: function(context) {
				context.font = 'bold 42px PressStart2P';  // 像素风格字体
				context.textAlign = 'center';
				context.textBaseline = 'middle';
				context.fillStyle = '#FFF';              // 白色文字
				context.fillText('Pac-Man', this.x, this.y);
			}
		});

		/**
		 * 操作提示文字（闪烁效果）
		 */
		stage.createItem({
			x: game.width / 2,          // 水平居中
			y: game.height * 0.64,      // 垂直位置：64%处
			frames: 28,                 // 闪烁频率控制
			draw: function(context) {
				// 每隔一帧显示文字，实现闪烁效果
				if (this.times % 2) {
					context.font = 'bold 14px PressStart2P';
					context.textAlign = 'center';
					context.textBaseline = 'middle';
					context.fillStyle = '#AAA';          // 灰色文字
					context.fillText('Press Enter to start', this.x, this.y);
				}
			}
		});
		//版权信息
		stage.createItem({
			x:game.width-10,
			y:game.height-5,
			draw:function(context){
				var text = '© passer-by.com';
				context.font = '12px/20px PressStart2P';
				context.textAlign = 'left';
				context.textBaseline = 'top';
				context.fillStyle = '#AAA';
				this.width = context.measureText(text).width;
				this.x = game.width-this.width-10;
				this.y = game.height-20-5;
				context.fillText(text,this.x,this.y);
			}
		}).bind('click',function(){
			window.open('https://passer-by.com');
		});
		//事件绑定
		stage.bind('keydown',function(e){
			switch(e.keyCode){
				case 13:
				case 32:
				game.nextStage();
				break;
			}
		});
	})();
	//游戏主程序
	(function(){
		_COIGIG.forEach(function(config,index){
			var stage,map,beans,items,player;
			stage = game.createStage({
				update:function(){
					var stage = this;
					if(stage.status==1){								//场景正常运行
						items.forEach(function(item){
							if(map&&!map.get(item.coord.x,item.coord.y)&&!map.get(player.coord.x,player.coord.y)){
								var dx = item.x-player.x;
								var dy = item.y-player.y;
								if(dx*dx+dy*dy<750&&item.status!=4){		//物体检测
									if(item.status==3){
										item.status = 4;
										_SCORE += 10;
									}else{
										stage.status = 3;
										stage.timeout = 30;
									}
								}
							}
						});
						if(JSON.stringify(beans.data).indexOf(0)<0){	//当没有物品的时候，进入下一关
							game.nextStage();
						}
					}else if(stage.status==3){		//场景临时状态
						if(!stage.timeout){
							_LIFE--;
							if(_LIFE){
								stage.resetItems();
							}else{
								var stages = game.getStages();
								game.setStage(stages.length-1);
								return false;
							}
						}
					}
				}
			});
			//绘制地图
			map = stage.createMap({
				x:60,
				y:10,
				data:config['map'],
				cache:true,
				draw:function(context){
					context.lineWidth = 2;
					for(var j=0; j<this.y_length; j++){
						for(var i=0; i<this.x_length; i++){
							var value = this.get(i,j);
							if(value){
								var code = [0,0,0,0];
								if(this.get(i+1,j)&&!(this.get(i+1,j-1)&&this.get(i+1,j+1)&&this.get(i,j-1)&&this.get(i,j+1))){
									code[0]=1;
								}
								if(this.get(i,j+1)&&!(this.get(i-1,j+1)&&this.get(i+1,j+1)&&this.get(i-1,j)&&this.get(i+1,j))){
									code[1]=1;
								}
								if(this.get(i-1,j)&&!(this.get(i-1,j-1)&&this.get(i-1,j+1)&&this.get(i,j-1)&&this.get(i,j+1))){
									code[2]=1;
								}
								if(this.get(i,j-1)&&!(this.get(i-1,j-1)&&this.get(i+1,j-1)&&this.get(i-1,j)&&this.get(i+1,j))){
									code[3]=1;
								}
								if(code.indexOf(1)>-1){
									context.strokeStyle=value==2?"#FFF":config['wall_color'];
									var pos = this.coord2position(i,j);
									switch(code.join('')){
										case '1100':
											context.beginPath();
											context.arc(pos.x+this.size/2,pos.y+this.size/2,this.size/2,Math.PI,1.5*Math.PI,false);
											context.stroke();
											context.closePath();
											break;
										case '0110':
											context.beginPath();
											context.arc(pos.x-this.size/2,pos.y+this.size/2,this.size/2,1.5*Math.PI,2*Math.PI,false);
											context.stroke();
											context.closePath();
											break;
										case '0011':
											context.beginPath();
											context.arc(pos.x-this.size/2,pos.y-this.size/2,this.size/2,0,.5*Math.PI,false);
											context.stroke();
											context.closePath();
											break;
										case '1001':
											context.beginPath();
											context.arc(pos.x+this.size/2,pos.y-this.size/2,this.size/2,.5*Math.PI,1*Math.PI,false);
											context.stroke();
											context.closePath();
											break;
										default:
											var dist = this.size/2;
											code.forEach(function(v,index){
												if(v){
													context.beginPath();
													context.moveTo(pos.x,pos.y);
													context.lineTo(pos.x-_COS[index]*dist,pos.y-_SIN[index]*dist);
													context.stroke();
													context.closePath();
												}
											});
									}
								}
							}
						}
					}
				}
			});
			//物品地图
			beans = stage.createMap({
				x:60,
				y:10,
				data:config['map'],
				frames:8,
				draw:function(context){
					for(var j=0; j<this.y_length; j++){
						for(var i=0; i<this.x_length; i++){
							if(!this.get(i,j)){
								var pos = this.coord2position(i,j);
								context.fillStyle = "#F5F5DC";
								if(config['goods'][i+','+j]){
									context.beginPath();
									context.arc(pos.x,pos.y,3+this.times%2,0,2*Math.PI,true);
									context.fill();
									context.closePath();
								}else{
									context.fillRect(pos.x-2,pos.y-2,4,4);
								}
							}
						}
					}
				}
			});
			//关卡得分
			stage.createItem({
				x:690,
				y:80,
				draw:function(context){
					context.font = 'bold 24px PressStart2P';
					context.textAlign = 'left';
					context.textBaseline = 'bottom';
					context.fillStyle = '#C33';
					context.fillText('SCORE',this.x,this.y);
					context.font = '24px PressStart2P';
					context.textAlign = 'left';
					context.textBaseline = 'top';
					context.fillStyle = '#FFF';
					context.fillText(_SCORE,this.x+12,this.y+10);
					context.font = 'bold 24px PressStart2P';
					context.textAlign = 'left';
					context.textBaseline = 'bottom';
					context.fillStyle = '#C33';
					context.fillText('LEVEL',this.x,this.y+72);
					context.font = '24px PressStart2P';
					context.textAlign = 'left';
					context.textBaseline = 'top';
					context.fillStyle = '#FFF';
					context.fillText(index+1,this.x+12,this.y+82);
				}
			});
			//状态文字
			stage.createItem({
				x:690,
				y:285,
				frames:25,
				draw:function(context){
					if(stage.status==2&&this.times%2){
						context.font = '24px PressStart2P';
						context.textAlign = 'left';
						context.textBaseline = 'center';
						context.fillStyle = '#FFF';
						context.fillText('PAUSE',this.x,this.y);
					}
				}
			});
			//生命值
			stage.createItem({
				x:705,
				y:510,
				width:30,
				height:30,
				draw:function(context){
					var max = Math.min(_LIFE-1,5);
					for(var i=0;i<max;i++){
						var x=this.x+40*i,y=this.y;
						context.fillStyle = '#FFE600';
						context.beginPath();
						context.arc(x,y,this.width/2,.15*Math.PI,-.15*Math.PI,false);
						context.lineTo(x,y);
						context.closePath();
						context.fill();
					}
					context.font = '18px PressStart2P';
					context.textAlign = 'left';
					context.textBaseline = 'center';
					context.fillStyle = '#FFF';
					context.fillText('X',this.x-15,this.y+30);
					context.font = '24px PressStart2P';
					context.fillText((_LIFE-1),this.x+10,this.y+26);
				}
			});
			//NPC
			for(var i=0;i<4;i++){
				stage.createItem({
					width:30,
					height:30,
					orientation:3,
					color:_COLOR[i],
					location:map,
					coord:{x:12+i,y:14},
					vector:{x:12+i,y:14},
					type:2,
					frames:10,
					speed:1,
					timeout:Math.floor(Math.random()*120),
					update:function(){
						var new_map;
						if(this.status==3&&!this.timeout){
							this.status = 1;
						}
						if(!this.coord.offset){			//到达坐标中心时计算
							if(this.status==1){
								if(!this.timeout){		//定时器
									new_map = JSON.parse(JSON.stringify(map.data).replace(/2/g,0));
									var id = this._id;
									items.forEach(function(item){
										if(item._id!=id&&item.status==1){	//NPC将其它所有还处于正常状态的NPC当成一堵墙
											new_map[item.coord.y][item.coord.x]=1;
										}
									});
									this.path = map.finder({
										map:new_map,
										start:this.coord,
										end:player.coord
									});
									if(this.path.length){
										this.vector = this.path[0];
									}
								}
							}else if(this.status==3){
								new_map = JSON.parse(JSON.stringify(map.data).replace(/2/g,0));
								var id = this._id;
								items.forEach(function(item){
									if(item._id!=id){
										new_map[item.coord.y][item.coord.x]=1;
									}
								});
								this.path = map.finder({
									map:new_map,
									start:player.coord,
									end:this.coord,
									type:'next'
								});
								if(this.path.length){
									this.vector = this.path[Math.floor(Math.random()*this.path.length)];
								}
							}else if(this.status==4){
								new_map = JSON.parse(JSON.stringify(map.data).replace(/2/g,0));
								this.path = map.finder({
									map:new_map,
									start:this.coord,
									end:this._params.coord
								});
								if(this.path.length){
									this.vector = this.path[0];
								}else{
									this.status = 1;
								}
							}
							//是否转变方向
							if(this.vector.change){
								this.coord.x = this.vector.x;
								this.coord.y = this.vector.y;
								var pos = map.coord2position(this.coord.x,this.coord.y);
								this.x = pos.x;
								this.y = pos.y;
							}
							//方向判定
							if(this.vector.x>this.coord.x){
								this.orientation = 0;
							}else if(this.vector.x<this.coord.x){
								this.orientation = 2;
							}else if(this.vector.y>this.coord.y){
								this.orientation = 1;
							}else if(this.vector.y<this.coord.y){
								this.orientation = 3;
							}
						}
						this.x += this.speed*_COS[this.orientation];
						this.y += this.speed*_SIN[this.orientation];
					},
					draw:function(context){
						var isSick = false;
						if(this.status==3){
							isSick = this.timeout>80||this.times%2?true:false;
						}
						if(this.status!=4){
							context.fillStyle = isSick?'#BABABA':this.color;
							context.beginPath();
							context.arc(this.x,this.y,this.width*.5,0,Math.PI,true);
							switch(this.times%2){
								case 0:
								context.lineTo(this.x-this.width*.5,this.y+this.height*.4);
								context.quadraticCurveTo(this.x-this.width*.4,this.y+this.height*.5,this.x-this.width*.2,this.y+this.height*.3);
								context.quadraticCurveTo(this.x,this.y+this.height*.5,this.x+this.width*.2,this.y+this.height*.3);
								context.quadraticCurveTo(this.x+this.width*.4,this.y+this.height*.5,this.x+this.width*.5,this.y+this.height*.4);
								break;
								case 1:
								context.lineTo(this.x-this.width*.5,this.y+this.height*.3);
								context.quadraticCurveTo(this.x-this.width*.25,this.y+this.height*.5,this.x,this.y+this.height*.3);
								context.quadraticCurveTo(this.x+this.width*.25,this.y+this.height*.5,this.x+this.width*.5,this.y+this.height*.3);
								break;
							}
							context.fill();
							context.closePath();
						}
						context.fillStyle = '#FFF';
						if(isSick){
							context.beginPath();
							context.arc(this.x-this.width*.15,this.y-this.height*.21,this.width*.08,0,2*Math.PI,false);
							context.arc(this.x+this.width*.15,this.y-this.height*.21,this.width*.08,0,2*Math.PI,false);
							context.fill();
							context.closePath();
						}else{
							context.beginPath();
							context.arc(this.x-this.width*.15,this.y-this.height*.21,this.width*.12,0,2*Math.PI,false);
							context.arc(this.x+this.width*.15,this.y-this.height*.21,this.width*.12,0,2*Math.PI,false);
							context.fill();
							context.closePath();
							context.fillStyle = '#000';
							context.beginPath();
							context.arc(this.x-this.width*(.15-.04*_COS[this.orientation]),this.y-this.height*(.21-.04*_SIN[this.orientation]),this.width*.07,0,2*Math.PI,false);
							context.arc(this.x+this.width*(.15+.04*_COS[this.orientation]),this.y-this.height*(.21-.04*_SIN[this.orientation]),this.width*.07,0,2*Math.PI,false);
							context.fill();
							context.closePath();
						}
					}
				});
			}
			items = stage.getItemsByType(2);
			//主角
			player = stage.createItem({
				width:30,
				height:30,
				type:1,
				location:map,
				coord:{x:13.5,y:23},
				orientation:2,
				speed:2,
				frames:10,
				update:function(){
					var coord = this.coord;
					if(!coord.offset){
						if(typeof this.control.orientation != 'undefined'){
							if(!map.get(coord.x+_COS[this.control.orientation],coord.y+_SIN[this.control.orientation])){
								this.orientation = this.control.orientation;
							}
						}
						this.control = {};
						var value = map.get(coord.x+_COS[this.orientation],coord.y+_SIN[this.orientation]);
						if(value==0){
							this.x += this.speed*_COS[this.orientation];
							this.y += this.speed*_SIN[this.orientation];
						}else if(value<0){
							this.x -= map.size*(map.x_length-1)*_COS[this.orientation];
							this.y -= map.size*(map.y_length-1)*_SIN[this.orientation];
						}
					}else{
						if(!beans.get(this.coord.x,this.coord.y)){	//吃豆
							_SCORE++;
							beans.set(this.coord.x,this.coord.y,1);
							if(config['goods'][this.coord.x+','+this.coord.y]){	//吃到能量豆
								items.forEach(function(item){
									if(item.status==1||item.status==3){	//如果NPC为正常状态，则置为临时状态
										item.timeout = 450;
										item.status = 3;
									}
								});
							}
						}
						this.x += this.speed*_COS[this.orientation];
						this.y += this.speed*_SIN[this.orientation];
					}
				},
				draw:function(context){
					context.fillStyle = '#FFE600';
					context.beginPath();
					if(stage.status!=3){	//玩家正常状态
						if(this.times%2){
							context.arc(this.x,this.y,this.width/2,(.5*this.orientation+.20)*Math.PI,(.5*this.orientation-.20)*Math.PI,false);
						}else{
							context.arc(this.x,this.y,this.width/2,(.5*this.orientation+.01)*Math.PI,(.5*this.orientation-.01)*Math.PI,false);
						}
					}else{	//玩家被吃
						if(stage.timeout) {
							context.arc(this.x,this.y,this.width/2,(.5*this.orientation+1-.02*stage.timeout)*Math.PI,(.5*this.orientation-1+.02*stage.timeout)*Math.PI,false);
						}
					}
					context.lineTo(this.x,this.y);
					context.closePath();
					context.fill();
				}
			});
			//事件绑定
			stage.bind('keydown',function(e){
				switch(e.keyCode){
					case 13: //回车
					case 32: //空格
					this.status = this.status==2?1:2;
					break;
					case 39: //右
					player.control = {orientation:0};
					break;
					case 40: //下
					player.control = {orientation:1};
					break;
					case 37: //左
					player.control = {orientation:2};
					break;
					case 38: //上
					player.control = {orientation:3};
					break;
				}
			});
		});
	})();
	//结束画面
	(function(){
		var stage = game.createStage();
		//游戏结束
		stage.createItem({
			x:game.width/2,
			y:game.height*.35,
			draw:function(context){
				context.fillStyle = '#FFF';
				context.font = 'bold 48px PressStart2P';
				context.textAlign = 'center';
				context.textBaseline = 'middle';
				context.fillText(_LIFE?'YOU WIN!':'GAME OVER',this.x,this.y);
			}
		});
		//记分
		stage.createItem({
			x:game.width/2,
			y:game.height*.5,
			draw:function(context){
				context.fillStyle = '#FFF';
				context.font = '20px PressStart2P';
				context.textAlign = 'center';
				context.textBaseline = 'middle';
				context.fillText('FINAL SCORE: '+(_SCORE+50*Math.max(_LIFE-1,0)),this.x,this.y);
			}
		});
		//事件绑定
		stage.bind('keydown',function(e){
			switch(e.keyCode){
				case 13: //回车
				case 32: //空格
				_SCORE = 0;
				_LIFE = 5;
				game.setStage(1);
				break;
			}
		});
	})();

	/* ========================================
	   字体加载和游戏初始化
	   ======================================== */

	/**
	 * 加载像素风格字体并启动游戏
	 * 使用FontFace API确保字体加载完成后再开始游戏
	 */
	const myFont = new FontFace('PressStart2P', 'url(./static/font/PressStart2P.ttf)');
	myFont.load().then(font => {
		document.fonts.add(font);    // 添加字体到文档
		game.init();                 // 启动游戏引擎
	});
})();

/* ========================================
   代码注释完成总结
   ======================================== */

/**
 * 吃豆人游戏代码注释完成
 *
 * 已添加详细注释的内容包括：
 *
 * 1. HTML文件 (index.html):
 *    - 文档结构说明
 *    - 元标签用途解释
 *    - Canvas元素配置
 *    - 脚本加载顺序说明
 *
 * 2. CSS文件 (static/style/index.css):
 *    - CSS Reset重置样式
 *    - 页面布局样式
 *    - 组件样式详解
 *    - 响应式设计说明
 *
 * 3. 游戏引擎 (static/script/game.js):
 *    - requestAnimationFrame兼容性处理
 *    - 游戏引擎架构说明
 *    - Item类：游戏对象管理
 *    - Map类：地图系统和坐标转换
 *    - Stage类：场景管理
 *    - 事件处理系统
 *    - 寻路算法实现
 *
 * 4. 游戏逻辑 (static/script/index.js):
 *    - 关卡配置数据结构
 *    - 全局变量说明
 *    - 启动页面实现
 *    - 游戏主循环逻辑
 *    - AI行为算法
 *    - 碰撞检测系统
 *    - 用户输入处理
 *
 * 这些注释帮助开发者理解：
 * - 代码架构和设计模式
 * - 游戏引擎的工作原理
 * - Canvas 2D图形绘制技术
 * - 游戏AI和寻路算法
 * - 事件驱动编程模式
 * - 面向对象的JavaScript编程
 */
