/*
 * 吃豆人游戏样式文件
 * 包含页面布局、组件样式和响应式设计
 */

/* ========================================
   CSS Reset - 重置浏览器默认样式
   ======================================== */

/* 设置HTML根元素的基本样式：黑色文字，白色背景 */
html {
    color: #000;
    background: #FFF;
}

/* 重置所有常用元素的外边距和内边距为0，确保跨浏览器一致性 */
body, div, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6,
pre, code, form, fieldset, legend, input, textarea, p, blockquote, th, td {
    margin: 0;
    padding: 0;
}

/* 表格样式重置：合并边框，去除单元格间距 */
table {
    border-collapse: collapse;
    border-spacing: 0;
}

/* 去除fieldset和img元素的默认边框 */
fieldset, img {
    border: 0;
}

/* 重置文本样式元素为正常字体样式和粗细 */
address, caption, cite, code, dfn, em, strong, th, var {
    font-style: normal;
    font-weight: normal;
}

/* 去除列表的默认样式（项目符号/数字） */
ol, ul {
    list-style: none;
}

/* 设置标题和表格标题左对齐 */
caption, th {
    text-align: left;
}

/* 重置标题元素的字体大小和粗细 */
h1, h2, h3, h4, h5, h6 {
    font-size: 100%;
    font-weight: normal;
}

/* 去除引用元素的前后内容 */
q:before, q:after {
    content: '';
}

/* 重置缩写元素样式 */
abbr, acronym {
    border: 0;
    font-variant: normal;
}

/* 设置上标和下标的垂直对齐方式 */
sup {
    vertical-align: text-top;
}

sub {
    vertical-align: text-bottom;
}

/* 表单元素继承父元素的字体样式 */
input, textarea, select {
    font-family: inherit;
    font-size: inherit;
    font-weight: inherit;
}

/* 图例元素文字颜色 */
legend {
    color: #000;
}
/* ========================================
   页面布局样式
   ======================================== */

/* 主容器：固定宽度，居中显示 */
.wrapper {
    width: 960px;           /* 固定宽度960px，适配游戏画布尺寸 */
    margin: 0 auto;         /* 水平居中 */
    color: #999;            /* 默认文字颜色为灰色 */
}

/* ========================================
   游戏面板样式 (.mod-panel)
   ======================================== */

/* 面板头部：游戏标题区域 */
.mod-panel .hd {
    padding-top: 10px;      /* 顶部内边距 */
    line-height: 40px;      /* 行高 */
    text-align: center;     /* 文字居中 */
}

/* 游戏标题样式 */
.mod-panel .hd h1 {
    font-size: 20px;        /* 字体大小 */
    font-weight: bold;      /* 粗体 */
    color: #333;            /* 深灰色文字 */
}

/* 游戏画布样式：黑色背景，块级显示 */
.mod-panel canvas {
    display: block;         /* 块级元素，独占一行 */
    background: #000;       /* 黑色背景，符合经典游戏风格 */
}

/* 操作信息提示区域 */
.mod-panel .info {
    padding: 10px 0;        /* 上下内边距 */
    margin-bottom: 5px;     /* 底部外边距 */
    line-height: 20px;      /* 行高 */
    text-align: center;     /* 文字居中 */
    color: #666;            /* 中等灰色文字 */
}

/* 信息提示段落样式 */
.mod-panel .info p {
    line-height: 20px;      /* 行高 */
    font-size: 14px;        /* 字体大小 */
    color: #666;            /* 中等灰色文字 */
}

/* 游戏介绍区域：带背景色和圆角 */
.mod-panel .intro {
    padding: 10px 15px;     /* 内边距：上下10px，左右15px */
    background: #f8f8f8;    /* 浅灰色背景 */
    border-radius: 5px;     /* 圆角边框 */
}

/* 介绍文字段落样式 */
.mod-panel .intro p {
    line-height: 22px;      /* 行高，增加可读性 */
    text-indent: 2em;       /* 首行缩进2个字符 */
    font-size: 14px;        /* 字体大小 */
    color: #666;            /* 中等灰色文字 */
}

/* ========================================
   按钮区域样式 (.mod-button)
   ======================================== */

/* 按钮容器：固定高度，居中对齐 */
.mod-button {
    height: 32px;           /* 固定高度 */
    padding: 15px 0;        /* 上下内边距 */
    margin-bottom: 15px;    /* 底部外边距 */
    text-align: center;     /* 内容居中 */
}

/* 按钮和链接的通用样式：行内块元素，垂直居中 */
.mod-button span, .mod-button a {
    display: inline-block;  /* 行内块元素，可设置宽高 */
    height: 28px;           /* 固定高度 */
    vertical-align: middle; /* 垂直居中对齐 */
}

/* 自定义按钮样式：模仿GitHub按钮风格 */
.mod-button .btn {
    display: inline-block;  /* 行内块元素 */
    height: 26px;           /* 按钮高度 */
    padding: 0 12px;        /* 左右内边距 */
    line-height: 26px;      /* 行高，实现垂直居中 */
    background-color: #ebf0f4; /* 浅蓝灰色背景 */
    border: 1px solid #d1d9e0;  /* 边框颜色 */
    text-decoration: none;  /* 去除下划线 */
    font-weight: bold;      /* 粗体文字 */
    font-size: 13px;        /* 字体大小 */
    color: #25292e;         /* 深色文字 */
    border-radius: 3px;     /* 圆角边框 */
}

/* 按钮悬停效果：背景色变浅 */
.mod-button .btn:hover {
    background: #f5f6f7;    /* 悬停时的背景色 */
}