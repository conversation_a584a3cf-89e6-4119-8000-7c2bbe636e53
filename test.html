<!DOCTYPE html>
<!--
    吃豆人游戏学习测试页面
    用于测试简化版游戏引擎
-->
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>吃豆人游戏 - 学习测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #222;
            color: #fff;
            font-family: Arial, sans-serif;
            text-align: center;
        }

        canvas {
            border: 2px solid #fff;
            background: #000;
            display: block;
            margin: 20px auto;
        }

        .controls {
            margin: 20px 0;
        }

        button {
            padding: 10px 20px;
            margin: 0 10px;
            font-size: 16px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }

        button:hover {
            background: #45a049;
        }

        button:disabled {
            background: #666;
            cursor: not-allowed;
        }

        .info {
            max-width: 800px;
            margin: 0 auto;
            text-align: left;
            background: #333;
            padding: 20px;
            border-radius: 10px;
        }

        .step {
            margin: 10px 0;
            padding: 10px;
            background: #444;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>🎮 吃豆人游戏 - 学习测试</h1>

    <!-- 游戏画布 -->
    <canvas id="gameCanvas" width="960" height="640">
        您的浏览器不支持Canvas
    </canvas>

    <!-- 控制按钮 -->
    <div class="controls">
        <button id="startBtn">开始游戏</button>
        <button id="stopBtn" disabled>停止游戏</button>
        <button id="addBoxBtn">添加方块</button>
        <button id="clearBtn">清除所有</button>
    </div>



    <!-- 引入简化版游戏引擎 -->
    <script src="./static/script/game-simple.js"></script>

    <script>
        /* ========================================
           测试页面JavaScript代码
           ======================================== */

        /**
         * 创建游戏引擎实例
         * 绑定到id为'gameCanvas'的Canvas元素
         */
        const game = new SimpleGame('gameCanvas');

        /* ---- 获取页面按钮元素 ---- */

        // 获取所有控制按钮的DOM引用
        // 这样可以为它们添加事件监听器
        const startBtn = document.getElementById('startBtn');    // 开始游戏按钮
        const stopBtn = document.getElementById('stopBtn');      // 停止游戏按钮
        const addBoxBtn = document.getElementById('addBoxBtn');  // 添加方块按钮
        const clearBtn = document.getElementById('clearBtn');    // 清除所有按钮

        /* ========================================
           事件监听器设置
           ======================================== */

        /**
         * 开始游戏按钮事件处理
         *
         * 功能：
         * 1. 检查是否有游戏对象，没有则创建一个
         * 2. 启动游戏循环
         * 3. 更新按钮状态（禁用开始按钮，启用停止按钮）
         */
        startBtn.addEventListener('click', function() {
            // 检查游戏中是否有对象
            // 如果没有对象，游戏启动后画面会是空的
            if (game.objects.length === 0) {
                // 创建一个默认的移动方块用于演示
                // 位置设置在(100, 100)，这样不会太靠近边界
                const box = new MovingBox(100, 100);
                game.addObject(box);
            }

            // 启动游戏引擎
            game.start();

            // 更新按钮状态：游戏运行时不能再次点击开始
            startBtn.disabled = true;   // 禁用开始按钮
            stopBtn.disabled = false;   // 启用停止按钮
        });

        /**
         * 停止游戏按钮事件处理
         *
         * 功能：
         * 1. 停止游戏循环
         * 2. 恢复按钮状态
         */
        stopBtn.addEventListener('click', function() {
            // 停止游戏引擎
            game.stop();

            // 恢复按钮状态：游戏停止后可以重新开始
            startBtn.disabled = false;  // 启用开始按钮
            stopBtn.disabled = true;    // 禁用停止按钮
        });

        /**
         * 添加方块按钮事件处理
         *
         * 功能：
         * 1. 在随机位置创建新的移动方块
         * 2. 设置随机颜色
         * 3. 添加到游戏中
         *
         * 这个功能用于测试多对象管理和性能
         */
        addBoxBtn.addEventListener('click', function() {
            // 生成随机位置
            // Math.random() 返回0-1之间的随机数
            // 需要确保方块不会超出画布边界，所以减去方块的宽度(30)
            const x = Math.random() * (960 - 30);  // X坐标：0 到 930
            const y = Math.random() * (640 - 30);  // Y坐标：0 到 610

            // 创建新的移动方块
            const box = new MovingBox(x, y);

            // 设置随机颜色
            // 预定义一些好看的颜色供随机选择
            const colors = [
                '#FFE600',  // 黄色（吃豆人色）
                '#FF0000',  // 红色
                '#00FF00',  // 绿色
                '#0000FF',  // 蓝色
                '#FF00FF',  // 紫色
                '#00FFFF'   // 青色
            ];

            // 随机选择一个颜色
            // Math.floor() 向下取整，确保得到有效的数组索引
            const randomIndex = Math.floor(Math.random() * colors.length);
            box.color = colors[randomIndex];

            // 将新方块添加到游戏中
            game.addObject(box);
        });

        /**
         * 清除所有对象按钮事件处理
         *
         * 功能：清空游戏中的所有对象
         * 注意：这不会停止游戏循环，只是清空对象列表
         */
        clearBtn.addEventListener('click', function() {
            // 直接清空对象数组
            // 这会让画面变成空的黑色背景
            game.objects = [];
        });

        /* ========================================
           页面初始化和调试信息
           ======================================== */

        /**
         * 页面加载完成事件处理
         *
         * 在页面完全加载后执行，用于：
         * 1. 输出调试信息到控制台
         * 2. 提供使用提示
         * 3. 确认所有资源已正确加载
         */
        window.addEventListener('load', function() {
            // 在浏览器控制台输出欢迎信息
            console.log('🎮 游戏引擎已加载完成！');
            console.log('💡 提示：打开开发者工具查看更多信息');
            console.log('📖 建议：先点击"开始游戏"测试基础功能');

            // 输出游戏引擎实例信息，方便调试
            console.log('🔧 游戏引擎实例：', game);
            console.log('📐 画布尺寸：', game.width, 'x', game.height);

            // 提供一些调试命令示例
            console.log('🛠️ 调试命令示例：');
            console.log('   game.objects - 查看所有游戏对象');
            console.log('   game.start() - 启动游戏');
            console.log('   game.stop() - 停止游戏');
            console.log('   game.addObject(new MovingBox(200, 200)) - 添加新方块');
        });
    </script>
</body>
</html>
